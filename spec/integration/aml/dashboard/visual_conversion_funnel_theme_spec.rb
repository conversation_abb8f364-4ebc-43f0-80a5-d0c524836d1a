# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Conversion Funnel theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  # Individual test cases for all scenarios with unified theme approach
  context 'when testing theme compatibility' do
    # Scenarios without themes
    it 'basic conversion funnel' do
      scenario = {
        name: 'basic conversion funnel',
        viz_type: :conversion_funnel,
        aml_params: {},
        table_selector: '[data-uname="v1"] .new-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'conversion funnel with custom circle color' do
      scenario = {
        name: 'conversion funnel with custom circle color',
        viz_type: :conversion_funnel,
        aml_params: { settings_aml: 'circle_color: "#FEDBDB"' },
        table_selector: '[data-uname="v1"] .new-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'conversion funnel with custom columns color' do
      scenario = {
        name: 'conversion funnel with custom columns color',
        viz_type: :conversion_funnel,
        aml_params: { settings_aml: 'columns_color: "#F8DDC4"' },
        table_selector: '[data-uname="v1"] .new-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'conversion funnel with both custom colors' do
      scenario = {
        name: 'conversion funnel with both custom colors',
        viz_type: :conversion_funnel,
        aml_params: {
          settings_aml: <<~SETTINGS,
            circle_color: "#FEDBDB"
            columns_color: "#F8DDC4"
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .new-conversion-funnel',
      }
      run_test_scenario(scenario, with_theme: false)
    end
  end
end

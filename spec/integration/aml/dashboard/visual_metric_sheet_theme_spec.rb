# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Metric Sheet theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('ag-grid:metric-sheet', true)
    FeatureToggle.toggle_global('ag-grid:allow_save_column_width_size', true)
  end

  # Individual test cases for all scenarios with unified theme approach
  context 'when testing theme compatibility' do
    # Scenarios without themes
    it 'basic table' do
      scenario = {
        name: 'basic table',
        viz_type: :metric_sheet,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-metric-sheet',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    # Scenarios with colorful theme
    context 'with colorful theme' do
      it 'basic table with colorful_table_theme' do
        scenario = {
          name: 'basic table',
          viz_type: :metric_sheet,
          aml_params: {},
          table_selector: '[data-uname="v1"] .h-metric-sheet',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :colorful_theme)
      end
    end

    # Scenarios with oasis theme
    context 'with oasis theme' do
      it 'basic table with oasis_table_theme' do
        scenario = {
          name: 'basic table',
          viz_type: :metric_sheet,
          aml_params: {},
          table_selector: '[data-uname="v1"] .h-metric-sheet',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :oasis_theme)
      end
    end

    # Scenarios with minimal theme
    context 'with minimal theme' do
      it 'basic table with minimal_table_theme' do
        scenario = {
          name: 'basic table',
          viz_type: :metric_sheet,
          aml_params: {},
          table_selector: '[data-uname="v1"] .h-metric-sheet',
        }
        run_test_scenario(scenario, with_theme: true, theme_key: :minimal_theme)
      end
    end
  end
end

# typed: false
# frozen_string_literal: true

require 'rails_helper'

describe 'Visual Regression: Retention Heatmap theme', :js, type: :feature do
  include_context 'visual_regression_theme'

  before do
    FeatureToggle.toggle_global('ag-grid:cohort-retention', true)
  end

  # Individual test cases for all scenarios with unified theme approach
  context 'when testing theme compatibility' do
    # Scenarios without themes
    it 'basic retention heatmap' do
      scenario = {
        name: 'basic retention heatmap',
        viz_type: :retention_heatmap,
        aml_params: {},
        table_selector: '[data-uname="v1"] .h-cohort-retention',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'basic retention heatmap range selection table' do
      scenario = {
        name: 'basic retention heatmap',
        viz_type: :retention_heatmap,
        aml_params: {},
        additional_steps: lambda {
          # performing range selection
          first_header = page.find('.ag-cell[comp-id="212"]') # Use uname as colId
          data_header = page.find('.ag-cell[comp-id="405"]')
          first_header.drag_to(data_header)
          sleep 1
        },
        table_selector: '[data-uname="v1"] .h-cohort-retention',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'retention heatmap with percentage display disabled' do
      scenario = {
        name: 'retention heatmap with percentage display disabled',
        viz_type: :retention_heatmap,
        aml_params: { settings_aml: 'display_percentage_values: false' },
        table_selector: '[data-uname="v1"] .h-cohort-retention',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'retention heatmap with convert empty to zero' do
      scenario = {
        name: 'retention heatmap with convert empty to zero',
        viz_type: :retention_heatmap,
        aml_params: { settings_aml: 'convert_empty_to_zero: true' },
        table_selector: '[data-uname="v1"] .h-cohort-retention',
      }
      run_test_scenario(scenario, with_theme: false)
    end

    it 'retention heatmap with both settings enabled' do
      scenario = {
        name: 'retention heatmap with both settings enabled',
        viz_type: :retention_heatmap,
        aml_params: {
          settings_aml: <<~SETTINGS,
            display_percentage_values: false
            convert_empty_to_zero: true
          SETTINGS
        },
        table_selector: '[data-uname="v1"] .h-cohort-retention',
      }
      run_test_scenario(scenario, with_theme: false)
    end
  end
end
